import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_utility/cubit/recommendation_list/broker_filter/broker_filter_cubit.dart';
import 'package:vp_utility/cubit/recommendation_list/broker_list/broker_list_bloc.dart';
import 'package:vp_utility/generated/l10n.dart';
import 'package:vp_utility/screen/recommendation_list/broker_list/widget/broker_list_rc.dart';
import 'package:vp_utility/screen/recommendation_list/broker_list/widget/general_rc_tab_view.dart';

class BrokerListScreen extends StatefulWidget {
  const BrokerListScreen({super.key});

  @override
  State<BrokerListScreen> createState() => _BrokerListScreenState();
}

class _BrokerListScreenState extends State<BrokerListScreen>
    with SingleTickerProviderStateMixin {
  late final TabController _controller;

  @override
  void initState() {
    super.initState();
    context.read<BrokerListCubit>().getBrokerRecommendationList();
    _controller = TabController(length: 2, vsync: this, initialIndex: 0);
    _controller.addListener(() {
      if (_controller.indexIsChanging) {
        _handleTabChange();
      }
    });
  }

  void _handleTabChange() {
    final newTabIndex = _controller.index;

    // Sync filter state from BrokerFilterCubit before switching tabs
    final brokerFilterCubit = context.read<BrokerFilterCubit>();
    final stockFilter = brokerFilterCubit.getFilterByIndex(0);
    final generalFilter = brokerFilterCubit.getFilterByIndex(1);

    // Update BrokerListCubit with synced filter state
    context.read<BrokerListCubit>()
      ..syncFilterFromBrokerFilterCubit(stockFilter, generalFilter)
      ..selectTab(newTabIndex);

    // Refresh data for the new tab with correct filter
    if (newTabIndex == 0) {
      if (stockFilter != null) {
        context.read<BrokerListCubit>().applyFilterForTab(0, stockFilter);
      } else {
        context.read<BrokerListCubit>().getBrokerRecommendationList(
          pageNo: 0,
          isRefresh: true,
        );
      }
    } else {
      if (generalFilter != null) {
        context.read<BrokerListCubit>().applyFilterForTab(1, generalFilter);
      } else {
        context.read<BrokerListCubit>().getGeneralRecommendationList(
          pageNo: 0,
          isRefresh: true,
        );
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colorUtils = Theme.of(context);
    return Column(
      children: [
        SizedBox(height: 8),
        Container(
          height: 40,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            color: colorUtils.highlightBg,
          ),
          margin: const EdgeInsets.symmetric(horizontal: 16),
          child: BlocBuilder<BrokerListCubit, BrokerListState>(
            buildWhen: (p, c) => p.tabIndex != c.tabIndex,
            builder:
                (context, state) => TabBar(
                  labelStyle: context.textStyle.subtitle14?.copyWith(
                    color: colorUtils.textEnable,
                  ),
                  unselectedLabelStyle: context.textStyle.body14?.copyWith(
                    color: colorUtils.white,
                  ),
                  labelColor: colorUtils.textEnable,
                  unselectedLabelColor: colorUtils.black,
                  padding: EdgeInsets.zero,
                  indicatorSize: TabBarIndicatorSize.tab,
                  controller: _controller,
                  indicator: BoxDecoration(
                    color: colorUtils.primary,
                    borderRadius: BorderRadius.only(
                      topLeft:
                          state.tabIndex == 0
                              ? const Radius.circular(4)
                              : Radius.zero,
                      bottomLeft:
                          state.tabIndex == 0
                              ? const Radius.circular(4)
                              : Radius.zero,
                      topRight:
                          state.tabIndex == 0
                              ? Radius.zero
                              : const Radius.circular(4),
                      bottomRight:
                          state.tabIndex == 0
                              ? Radius.zero
                              : const Radius.circular(4),
                    ),
                  ),

                  tabs: [
                    Tab(child: Text(S.current.rc_stockRc)),
                    Tab(child: Text(S.current.rc_generalRc)),
                  ],
                ),
          ),
        ),
        SizedBox(height: 8),
        Expanded(
          child: TabBarView(
            controller: _controller,
            children: const [BrokerListRC(), GeneralRcTabView()],
          ),
        ),
      ],
    );
  }
}
