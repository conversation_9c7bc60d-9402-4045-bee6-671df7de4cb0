import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:vp_common/error/show_error.dart';
import 'package:vp_common/utils/app_time_utils.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_utility/core/repository/recommendation_repository.dart';
import 'package:vp_utility/model/request/rc_param_filtter.dart';
import 'package:vp_utility/model/response/recommendation/broker_recommendation_model.dart';
import 'package:vp_utility/model/response/recommendation/rc_general_model.dart';

part 'broker_list_bloc.freezed.dart';
part 'broker_list_state.dart';

class BrokerListCubit extends Cubit<BrokerListState> {
  BrokerListCubit() : super(BrokerListState());
  final RecommendationRepository _recommendationRepository =
      GetIt.instance<RecommendationRepository>();

  bool _hasMoreBroker = true;
  bool _hasMoreGeneral = true;
  final int _pageSize = 20;
  Future<void> init() async {
    await Future.wait([
      getBrokerRecommendationList(pageNo: 0, isRefresh: true),
      getGeneralRecommendationList(pageNo: 0, isRefresh: true),
    ]);
  }

  /// Sync filter state from BrokerFilterCubit when switching tabs
  void syncFilterFromBrokerFilterCubit(
    RcParamFilter? stockFilter,
    RcParamFilter? generalFilter,
  ) {
    emit(
      state.copyWith(
        rcParamFilterBroker: stockFilter,
        rcParamFilterGeneral: generalFilter,
      ),
    );
  }

  void selectTab(int index) {
    emit(state.copyWith(tabIndex: index));
  }

  Future<void> getBrokerRecommendationList({
    int? pageNo,
    bool isRefresh = false,
  }) async {
    try {
      final int currentPage = pageNo ?? (isRefresh ? 0 : state.pageNoBroker);
      if (isRefresh) {
        _hasMoreBroker = true;
      }
      emit(state.copyWith(isLoading: currentPage == 0));
      final data = await _recommendationRepository.getRecommendation(
        RcRequestParam(
          pageNo: currentPage,
          pageSize: _pageSize,
          statusRc: state.rcParamFilterBroker?.statusTypeInit?.toRequest(),
          symbols: state.rcParamFilterBroker?.symbol?.toUpperCase(),
        ),
      );
      final newData = data.data ?? [];
      final allData =
          isRefresh || currentPage == 0
              ? newData
              : [...state.brokerRCList, ...newData];
      _hasMoreBroker = newData.length == _pageSize;
      emit(
        state.copyWith(
          brokerRCList: allData,
          isLoading: false,
          pageNoBroker: currentPage,
        ),
      );
    } catch (err) {
      emit(
        state.copyWith(
          isLoading: false,
          errorMessage: await getErrorMessage(err),
        ),
      );
    }
  }

  Future<void> getGeneralRecommendationList({
    int? pageNo,
    bool isRefresh = false,
  }) async {
    try {
      final int currentPage = pageNo ?? (isRefresh ? 0 : state.pageNoGeneral);
      if (isRefresh) {
        _hasMoreGeneral = true;
      }
      emit(state.copyWith(isLoading: currentPage == 0));
      final data = await _recommendationRepository.getGeneralRecommendation(
        RcRequestParam(
          pageNo: currentPage,
          pageSize: _pageSize,
          fromDate: AppTimeUtils.format(
            state.rcParamFilterGeneral?.dateTimeRangeCustom?.start,
            AppTimeUtilsFormat.dateNormal,
          ),
          toDate: AppTimeUtils.format(
            state.rcParamFilterGeneral?.dateTimeRangeCustom?.end,
            AppTimeUtilsFormat.dateNormal,
          ),
        ),
      );
      final newData = data.data ?? [];
      final allData =
          isRefresh || currentPage == 0
              ? newData
              : [...state.generalRCList, ...newData];
      _hasMoreGeneral = newData.length == _pageSize;
      emit(
        state.copyWith(
          generalRCList: allData,
          isLoading: false,
          pageNoGeneral: currentPage,
        ),
      );
    } catch (err) {
      emit(
        state.copyWith(
          isLoading: false,
          errorMessage: await getErrorMessage(err),
        ),
      );
    }
  }

  Future<void> refreshData() async {
    if (state.tabIndex == 0) {
      await getBrokerRecommendationList(pageNo: 0, isRefresh: true);
    } else {
      await getGeneralRecommendationList(pageNo: 0, isRefresh: true);
    }
  }

  Future<void> loadMoreData() async {
    if (state.isLoadingMore) return;

    if (state.tabIndex == 0) {
      if (!_hasMoreBroker) return;
      emit(state.copyWith(isLoadingMore: true));
      final nextPage = state.pageNoBroker + 1;
      await getBrokerRecommendationList(pageNo: nextPage);
    } else {
      if (!_hasMoreGeneral) return;
      emit(state.copyWith(isLoadingMore: true));
      final nextPage = state.pageNoGeneral + 1;
      await getGeneralRecommendationList(pageNo: nextPage);
    }
    emit(state.copyWith(isLoadingMore: false));
  }

  void setFilter(RcParamFilter filter) {
    if (state.tabIndex == 0) {
      emit(state.copyWith(rcParamFilterBroker: filter, pageNoBroker: 0));
      getBrokerRecommendationList(pageNo: 0, isRefresh: true);
    } else {
      emit(state.copyWith(rcParamFilterGeneral: filter, pageNoGeneral: 0));
      getGeneralRecommendationList(pageNo: 0, isRefresh: true);
    }
  }

  /// Apply filter for specific tab index and refresh data
  void applyFilterForTab(int tabIndex, RcParamFilter filter) {
    if (tabIndex == 0) {
      emit(state.copyWith(rcParamFilterBroker: filter, pageNoBroker: 0));
      getBrokerRecommendationList(pageNo: 0, isRefresh: true);
    } else {
      emit(state.copyWith(rcParamFilterGeneral: filter, pageNoGeneral: 0));
      getGeneralRecommendationList(pageNo: 0, isRefresh: true);
    }
  }
}
