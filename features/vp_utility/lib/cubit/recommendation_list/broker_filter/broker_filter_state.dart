import 'package:equatable/equatable.dart';
import 'package:vp_utility/model/request/rc_param_filtter.dart';

class BrokerFilterState extends Equatable {
  const BrokerFilterState({this.stockFilter, this.generalFilter});

  final RcParamFilter? stockFilter;
  final RcParamFilter? generalFilter;

  factory BrokerFilterState.initial() => const BrokerFilterState();

  BrokerFilterState copyWith({
    RcParamFilter? stockFilter,
    RcParamFilter? generalFilter,
    bool clearStockFilter = false,
    bool clearGeneralFilter = false,
  }) {
    return BrokerFilterState(
      stockFilter: clearStockFilter ? null : (stockFilter ?? this.stockFilter),
      generalFilter:
          clearGeneralFilter ? null : (generalFilter ?? this.generalFilter),
    );
  }

  @override
  List<Object?> get props => [stockFilter, generalFilter];
}

extension BrokerFilterStateExtension on BrokerFilterState {
  RcParamFilter? getFilterByIndex(int tabIndex) {
    switch (tabIndex) {
      case 0:
        return stockFilter;
      case 1:
        return generalFilter;
      default:
        return null;
    }
  }

  bool hasFilterForIndex(int tabIndex) {
    final filter = getFilterByIndex(tabIndex);
    if (filter == null) return false;
    return !filter.isDefault();
  }
}
